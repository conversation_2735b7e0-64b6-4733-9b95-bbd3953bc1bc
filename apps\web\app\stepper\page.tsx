"use client";

import * as React from "react";
import {
  StepperVertical,
  Step,
  StepPreviousButton,
  StepNextButton,
  StepActivationButton,
  StepContentActive,
  StepContentInactive,
  type StepConfig,
} from "@workspace/ui/components/stepper-vertical";

const steps: StepConfig[] = [
  { id: "kisisel", label: "Kişisel Bilgiler", completed: true },
  { id: "adres", label: "Adres Bilgileri", completed: true },
  { id: "odeme", label: "Ödeme Yöntemi", completed: true },
  { id: "teslimat", label: "Teslimat Seçenekleri" },
  { id: "sozlesme", label: "Sözleşme" },
  { id: "onay", label: "İnce<PERSON><PERSON> ve Onay" },
];

export default function StepperShowcase() {
  const [currentStep, setCurrentStep] = React.useState(0);

  // Form data states
  const [firstName, setFirstName] = React.useState("Ahmet");
  const [lastName, setLastName] = React.useState("Yılmaz");
  const [email, setEmail] = React.useState("<EMAIL>");

  const [street, setStreet] = React.useState("Atatürk Caddesi No:123");
  const [city, setCity] = React.useState("İstanbul");
  const [zip, setZip] = React.useState("34000");
  const [country, setCountry] = React.useState("Türkiye");

  const [paymentMethod, setPaymentMethod] = React.useState("credit-card");
  const [cardNumber, setCardNumber] = React.useState("**** **** **** 1234");

  const [deliveryMethod, setDeliveryMethod] = React.useState("fast-cargo");

  const [contractAccepted, setContractAccepted] = React.useState(true);

  return (
    <div className="container mx-auto p-8 w-2xl">
      <div className="text-center space-y-4 mb-8">
        <h1 className="text-4xl font-bold">Dikey Adım Bileşeni</h1>
        <p className="text-lg text-muted-foreground">
          Navigasyon ve içerik yönetimi ile dikey adım bileşeni
        </p>
      </div>

      <div className="border rounded-lg p-6 bg-card max-w-4xl mx-auto">
        <StepperVertical
          steps={steps}
          currentStep={currentStep}
          onStepChange={setCurrentStep}
        >
          <div className="flex flex-col">
            {/* Kişisel Bilgiler */}
            <Step stepId="kisisel">
              <StepContentActive>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Kişisel Bilgiler</h3>
                  <p className="text-muted-foreground">
                    Lütfen temel kişisel bilgilerinizi girin.
                  </p>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Ad</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Adınızı girin"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Soyad</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Soyadınızı girin"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2 col-span-2">
                      <label className="text-sm font-medium">E-posta</label>
                      <input
                        type="email"
                        className="w-full p-2 border rounded-md"
                        placeholder="E-posta adresinizi girin"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive>
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg border border-dashed">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        ✓ Kişisel Bilgiler Tamamlandı
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Ad, soyad ve diğer kişisel bilgiler kaydedildi
                      </p>
                    </div>
                    <StepActivationButton stepId="kisisel">
                      Düzenle
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>• Ad: {firstName}</p>
                    <p>• Soyad: {lastName}</p>
                    <p>• E-posta: {email}</p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>

            {/* Adres Bilgileri */}
            <Step stepId="adres">
              <StepContentActive>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Adres Bilgileri</h3>
                  <p className="text-muted-foreground">
                    Adres bilgilerinizi girin.
                  </p>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Sokak Adresi
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Sokak adresinizi girin"
                        value={street}
                        onChange={(e) => setStreet(e.target.value)}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Şehir</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Şehrinizi girin"
                          value={city}
                          onChange={(e) => setCity(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Posta Kodu
                        </label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Posta kodunu girin"
                          value={zip}
                          onChange={(e) => setZip(e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Ülke</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Ülkenizi girin"
                        value={country}
                        onChange={(e) => setCountry(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive>
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg border border-dashed">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        ✓ Adres Bilgileri Tamamlandı
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Teslimat adresi ve iletişim bilgileri kaydedildi
                      </p>
                    </div>
                    <StepActivationButton stepId="adres">
                      Düzenle
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>• Sokak: {street}</p>
                    <p>• Şehir: {city}</p>
                    <p>• Posta Kodu: {zip}</p>
                    <p>• Ülke: {country}</p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>

            {/* Ödeme Yöntemi */}
            <Step stepId="odeme">
              <StepContentActive>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Ödeme Yöntemi</h3>
                  <p className="text-muted-foreground">
                    Tercih ettiğiniz ödeme yöntemini seçin.
                  </p>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="payment"
                        className="w-4 h-4"
                        value="credit-card"
                        checked={paymentMethod === "credit-card"}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                      />
                      <span>Kredi Kartı</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="payment"
                        className="w-4 h-4"
                        value="paypal"
                        checked={paymentMethod === "paypal"}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                      />
                      <span>PayPal</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="payment"
                        className="w-4 h-4"
                        value="bank-transfer"
                        checked={paymentMethod === "bank-transfer"}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                      />
                      <span>Banka Havalesi</span>
                    </label>
                  </div>
                  {paymentMethod === "credit-card" && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Kart Numarası
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Kart numaranızı girin"
                        value={cardNumber}
                        onChange={(e) => setCardNumber(e.target.value)}
                      />
                    </div>
                  )}
                </div>
              </StepContentActive>
              <StepContentInactive>
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg border border-dashed">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        ✓ Ödeme Yöntemi Seçildi
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Güvenli ödeme yöntemi belirlendi ve doğrulandı
                      </p>
                    </div>
                    <StepActivationButton stepId="odeme">
                      Değiştir
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>
                      • Yöntem:{" "}
                      {paymentMethod === "credit-card"
                        ? "Kredi Kartı"
                        : paymentMethod === "paypal"
                          ? "PayPal"
                          : "Banka Havalesi"}
                    </p>
                    {paymentMethod === "credit-card" && (
                      <p>• Kart: {cardNumber}</p>
                    )}
                    <p>• Banka: Ziraat Bankası</p>
                    <p>• Güvenlik: 3D Secure aktif</p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>

            {/* Teslimat Seçenekleri */}
            <Step stepId="teslimat">
              <StepContentActive>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">
                    Teslimat Seçenekleri
                  </h3>
                  <p className="text-muted-foreground">
                    Size en uygun teslimat yöntemini seçin.
                  </p>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="delivery"
                        className="w-4 h-4"
                        value="standard-cargo"
                        checked={deliveryMethod === "standard-cargo"}
                        onChange={(e) => setDeliveryMethod(e.target.value)}
                      />
                      <span>Standart Kargo</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="delivery"
                        className="w-4 h-4"
                        value="fast-cargo"
                        checked={deliveryMethod === "fast-cargo"}
                        onChange={(e) => setDeliveryMethod(e.target.value)}
                      />
                      <span>Hızlı Kargo</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="delivery"
                        className="w-4 h-4"
                        value="store-pickup"
                        checked={deliveryMethod === "store-pickup"}
                        onChange={(e) => setDeliveryMethod(e.target.value)}
                      />
                      <span>Mağazadan Teslim Al</span>
                    </label>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive>
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg border border-dashed">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        ✓ Teslimat Seçeneği Belirlendi
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Kargo firması ve teslimat türü seçildi
                      </p>
                    </div>
                    <StepActivationButton stepId="teslimat">
                      Değiştir
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>
                      • Yöntem:{" "}
                      {deliveryMethod === "fast-cargo"
                        ? "Hızlı Kargo"
                        : deliveryMethod === "standard-cargo"
                          ? "Standart Kargo"
                          : "Mağazadan Teslim Al"}
                    </p>
                    <p>• Firma: Yurtiçi Kargo</p>
                    <p>• Tahmini Teslimat: 1-2 iş günü</p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>

            {/* Sözleşme */}
            <Step stepId="sozlesme">
              <StepContentActive>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Sözleşme</h3>
                  <p className="text-muted-foreground">
                    Hizmet sözleşmesini okuyup onaylayın.
                  </p>
                  <div className="space-y-4">
                    <div className="h-48 overflow-y-auto border rounded-md p-4 text-sm text-muted-foreground space-y-2">
                      <p>
                        <strong>1. Taraflar:</strong> Bu sözleşme, hizmet
                        sağlayıcı (bundan sonra "Şirket" olarak anılacaktır) ile
                        hizmetten faydalanan kullanıcı (bundan sonra "Kullanıcı"
                        olarak anılacaktır) arasında akdedilmiştir.
                      </p>
                      <p>
                        <strong>2. Sözleşmenin Konusu:</strong> İşbu sözleşme,
                        Şirket tarafından sunulan hizmetlerin kullanım
                        koşullarını ve tarafların hak ve yükümlülüklerini
                        düzenlemektedir.
                      </p>
                      <p>
                        <strong>3. Kullanım Koşulları:</strong> Kullanıcı,
                        hizmeti kullanırken yasalara ve Şirket tarafından
                        belirtilen kurallara uymayı kabul eder. Aksi davranışlar
                        hesabın askıya alınmasına veya sonlandırılmasına neden
                        olabilir.
                      </p>
                      <p>
                        <strong>4. Gizlilik:</strong> Kullanıcı bilgileri,
                        Gizlilik Politikamızda belirtilen koşullar çerçevesinde
                        korunmaktadır.
                      </p>
                      <p>
                        <strong>5. Değişiklikler:</strong> Şirket, bu sözleşmede
                        tek taraflı olarak değişiklik yapma hakkını saklı tutar.
                        Değişiklikler web sitesinde yayınlandığı an yürürlüğe
                        girer.
                      </p>
                    </div>
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        className="w-4 h-4"
                        checked={contractAccepted}
                        onChange={(e) => setContractAccepted(e.target.checked)}
                      />
                      <span>Sözleşmeyi okudum ve kabul ediyorum.</span>
                    </label>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive>
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg border border-dashed">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        ✓ Sözleşme Onaylandı
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Hizmet ve üyelik sözleşmesi kabul edildi
                      </p>
                    </div>
                    <StepActivationButton stepId="sozlesme">
                      İncele
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>• Sözleşme Türü: Bireysel Hizmet Sözleşmesi</p>
                    <p>
                      • Onay Tarihi: {new Date().toLocaleDateString("tr-TR")}
                    </p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>

            {/* İnceleme ve Onay */}
            <Step stepId="onay">
              <StepContentActive>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">İnceleme ve Onay</h3>
                  <p className="text-muted-foreground">
                    Lütfen bilgilerinizi gözden geçirin ve onaylayın.
                  </p>
                  <div className="bg-muted p-4 rounded-md space-y-2">
                    <p>
                      <strong>Ad Soyad:</strong> {firstName} {lastName}
                    </p>
                    <p>
                      <strong>Adres:</strong> {street}, {city}, {zip}
                    </p>
                    <p>
                      <strong>Ödeme:</strong>{" "}
                      {paymentMethod === "credit-card"
                        ? "Kredi Kartı"
                        : paymentMethod === "paypal"
                          ? "PayPal"
                          : "Banka Havalesi"}
                    </p>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive>
                <div className="space-y-4 p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-green-800">
                        ✓ Başvuru Tamamlandı
                      </p>
                      <p className="text-xs text-green-600">
                        Tüm bilgiler onaylandı ve işlem başarıyla tamamlandı
                      </p>
                    </div>
                    <StepActivationButton stepId="onay">
                      Görüntüle
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-green-700 space-y-1">
                    <p>• Başvuru No: #2024-001234</p>
                    <p>• Tarih: {new Date().toLocaleDateString("tr-TR")}</p>
                    <p>• Durum: Onaylandı</p>
                    <p>• Tahmini İşlem Süresi: 2-3 iş günü</p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-4">
            <StepPreviousButton />
            <StepNextButton />
          </div>
        </StepperVertical>
      </div>
    </div>
  );
}
