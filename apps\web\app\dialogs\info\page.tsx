import { InfoDialog } from "@/components/dialogs/info.dialog";

const items = [
  "Platformda kullanıcı adınızı değiştirme hakkınız ayda bir kez ile sınırlıdır.",
  "Diğer kullanıcılara karşı saygılı ve hoşgörülü olunuz.",
  "Profil fotoğraflarında uygunsuz içerik kullanılması yasaktır.",
  "Hizmet alımlarında belirlenen sürelere uyulması zorunludur.",
  "Ödemelerde herhangi bir sorun yaşarsanız destek ekibimizle iletişime geçebilirsiniz.",
  "Platform içi mesajlaşmalarda spam ve taciz içerikli mesajlar yasaktır.",
  "Hizmet veren kullanıcıların belirtilen saatlerde müsait olması gerekmektedir.",
  "Site içi işlemlerde Soda para birimi kullanılmaktadır.",
  "Hesap güvenliğiniz için düzenli olarak şifrenizi değiştirmenizi öneririz.",
  "Platform güncellemeleri hakkında bilgilendirmeler e-posta yoluyla yapılacaktır.",
  "Kullanıcılar arası para transferi işlemleri 24 saat içinde tamamlanmaktadır.",
  "Profil bilgilerinizi güncel tutmanız önemle rica olunur.",
  "Platform üzerinden yapılan görüşmeler kayıt altına alınmaktadır.",
  "Hizmet iptalleri en az 24 saat öncesinden yapılmalıdır.",
  "Kullanıcı geri bildirimleri platformun gelişimi için önemlidir.",
  "Referans sistemi ile arkadaşlarınızı davet edebilirsiniz.",
  "Platform içi ödemelerde komisyon oranları sabittir.",
  "Destek talepleriniz 48 saat içinde yanıtlanacaktır.",
  "Kullanıcı sözleşmesi şartlarına uyulması zorunludur.",
  "Hizmet kalitesi düzenli olarak denetlenmektedir.",
  "Özel mesajlarda kişisel bilgi paylaşımı önerilmemektedir.",
  "Platform içi reklamlar kullanıcı deneyimini etkilemeyecek şekilde düzenlenmiştir.",
  "Hesap silme işlemi geri alınamaz şekilde gerçekleşir.",
  "Kullanıcı puanlama sistemi hizmet kalitesini artırmaya yöneliktir.",
  "Teknik sorunlar için 7/24 destek hizmeti verilmektedir.",
];

export default function InfoDialogPage() {
  return <InfoDialog open={true} items={items} />;
}
