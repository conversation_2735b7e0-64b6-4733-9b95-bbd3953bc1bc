import { OrderConfirmationDialog } from "@/components/dialogs/order-confirmation.dialog";
import avatar from "./avatar.jpg";
import activity from "./activity.jpg";

export default async function Page() {
  return (
    <OrderConfirmationDialog
      open={true}
      username="<PERSON><PERSON> Maximus-sama"
      unitCount={2}
      unitType="turnuva"
      activityTitle="Mobile Legends: Bang Bang"
      avatar={{
        src: avatar.src,
        alt: "avatar",
      }}
      activity={{
        src: activity.src,
        alt: "activity",
      }}
      mainItem={{
        name: "Summoner’s Rift (Fancy Bro Edition)",
        sodaAmount: "100",
      }}
      serviceModifiers={[
        { name: "<PERSON><PERSON><PERSON>", sodaAmount: "2" },
        { name: "Dio Cosplay", sodaAmount: "4" },
        {
          name: "Discord grubumuzda istediğin gibi at koşturma",
          sodaAmount: "10",
        },
        { name: "<PERSON><PERSON> Selfie", sodaAmount: "1" },
        { name: "<PERSON><PERSON> Balığı Taklidi", sodaAmount: "3" },
        { name: "Oyunda arkadaş ekleme", sodaAmount: "2" },
      ]}
      totalSodaAmount={(100 + 2 + 4 + 10 + 1 + 3 + 2) * 2}
    />
  );
}
