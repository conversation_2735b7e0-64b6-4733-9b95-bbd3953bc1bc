"use client";

import { useRef, useState, useEffect } from "react";
import { Minus, Plus } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { Input } from "@workspace/ui/components/input";
import {
  ButtonGroup,
  ButtonGroupItem,
} from "@workspace/ui/components/button-group";

export interface QuantityPickerProps {
  value: number;
  onChange: (value: number) => void;
  onBlur?: () => void;
  min?: number;
  max?: number;
  label?: string;
  className?: string;
  disabled?: boolean;
}

export function QuantityPicker({
  value,
  onChange,
  onBlur,
  min = 1,
  max = 20,
  label = "adet",
  className,
  disabled = false,
}: QuantityPickerProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [inputValue, setInputValue] = useState<string>(value.toString());

  // Sync input value when external value changes
  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  const handleDecrement = () => {
    if (disabled) return;
    const newValue = Math.max(min, value - 1);
    setInputValue(newValue.toString());
    onChange(newValue);
    onBlur?.();
  };

  const handleIncrement = () => {
    if (disabled) return;
    const newValue = Math.min(max, value + 1);
    setInputValue(newValue.toString());
    onChange(newValue);
    onBlur?.();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;
    const newInputValue = e.target.value;

    // Update the input display value immediately (allows deletion and typing)
    setInputValue(newInputValue);

    // If not empty, try to parse and update the actual value
    if (newInputValue !== "") {
      const numValue = parseInt(newInputValue);
      if (!isNaN(numValue)) {
        onChange(numValue);
      }
    }
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (disabled) return;

    const currentInputValue = e.target.value;
    let finalValue = value; // Default to current value

    if (currentInputValue === "" || currentInputValue === "0") {
      // If empty or zero, set to minimum
      finalValue = min;
    } else {
      const numValue = parseInt(currentInputValue);
      if (!isNaN(numValue)) {
        // Clamp the value between min and max
        finalValue = Math.max(min, Math.min(max, numValue));
      }
    }

    // Update both the display value and the actual value
    setInputValue(finalValue.toString());
    onChange(finalValue);

    // Call the original onBlur if provided
    onBlur?.();
  };

  const handleInputContainerClick = () => {
    if (disabled) return;
    inputRef.current?.focus();
  };

  return (
    <ButtonGroup className={className}>
      <ButtonGroupItem
        onClick={handleDecrement}
        disabled={disabled || value <= min}
      >
        <Minus />
      </ButtonGroupItem>
      <ButtonGroupItem
        asChild
        className={cn(
          "p-0 focus-within:outline-4 -outline-offset-3 cursor-text",
          disabled && "opacity-50 cursor-not-allowed",
        )}
        onClick={handleInputContainerClick}
      >
        <div className="flex justify-center pl-5 pr-7 py-2 min-w-40">
          <div className="flex items-end gap-1.5">
            <Input
              ref={inputRef}
              type="number"
              value={inputValue}
              onChange={handleInputChange}
              onBlur={handleInputBlur}
              disabled={disabled}
              min={min}
              max={max}
              className="h-full p-0 bg-transparent border-0 md:text-lg text-right outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none font-mono font-bold"
              style={{ width: inputValue.length * 12 + "px" }}
            />
            <span className="text-left">{label}</span>
          </div>
        </div>
      </ButtonGroupItem>

      <ButtonGroupItem
        onClick={handleIncrement}
        disabled={disabled || value >= max}
      >
        <Plus />
      </ButtonGroupItem>
    </ButtonGroup>
  );
}
