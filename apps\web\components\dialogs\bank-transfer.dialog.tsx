"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@workspace/ui/components/dialog";

import { LabelSection } from "@workspace/ui/components/label-section";
import { Separator } from "@workspace/ui/components/separator";
import { CopyButton } from "@workspace/ui/components/copy-button";
import { AlertShape } from "@workspace/ui/components/shapes/alert-shape";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { InfoDialog } from "./info.dialog";
import { useBanks, type Bank } from "@/hooks/queries/banks";
import Image from "next/image";

interface BankTransferDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children?: React.ReactNode;
}

export function BankTransferDialog({
  open = false,
  onOpenChange,
  children,
}: BankTransferDialogProps) {
  const { data, isLoading, error } = useBanks();
  const [selectedBank, setSelectedBank] = useState<Bank | null>(null);

  // Set first bank as selected when data loads
  useEffect(() => {
    if (data?.banks && data.banks.length > 0 && !selectedBank) {
      setSelectedBank(data.banks[0]!);
    }
  }, [data?.banks, selectedBank]);

  const handleBankSelect = (
    bank: Bank,
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    setSelectedBank(bank);
    event.currentTarget.scrollIntoView({
      behavior: "smooth",
      inline: "center",
      block: "center",
    });
  };

  const renderBankList = () => {
    if (isLoading) {
      return (
        <div className="flex flex-row sm:flex-col my-2 sm:m-0 gap-3 pr-4 p-2">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton
              key={index}
              className="w-40 sm:w-full h-20 flex-shrink-0"
            />
          ))}
        </div>
      );
    }

    if (error || !data?.banks) {
      return (
        <div className="flex items-center justify-center h-20 text-muted-foreground">
          Bankalar yüklenemedi
        </div>
      );
    }

    return (
      <div className="flex flex-row sm:flex-col my-2 sm:m-0 gap-3 pr-4 p-2  after:content-[''] after:pr-2 sm:after:content-none">
        {data.banks.map((bank) => (
          <button
            key={bank.name}
            onClick={(event) => handleBankSelect(bank, event)}
            className="border-3 rounded-sm overflow-hidden border-background ring-foreground ring-2 w-40 sm:w-full h-20 relative flex-shrink-0"
          >
            <Image
              src={bank.image}
              alt={bank.name}
              fill
              className="object-cover"
            />
            {selectedBank?.name === bank.name && (
              <div
                id="selected-badge"
                className="bg-primary absolute px-3 skew-x-24 text-xs top-0 -right-1 rounded-xs"
              >
                <div className="-skew-x-24">{"seçildi"}</div>
              </div>
            )}
          </button>
        ))}
      </div>
    );
  };

  const renderBankInfo = () => {
    if (isLoading) {
      return (
        <article
          id="bank-info"
          className="mt-1.5 p-4 sm:col-span-5 bg-muted rounded-lg flex flex-col h-full overflow-auto scrollbar"
        >
          <div className="flex justify-between items-start">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-8 w-8" />
          </div>
          <Skeleton className="h-4 w-full mb-5 mt-2" />
          <Separator />
          <div className="flex justify-between items-start">
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-8 w-8" />
          </div>
          <Skeleton className="h-4 w-3/4 mb-5 mt-2" />
          <Separator />
          <div className="flex justify-between items-start">
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-8 w-8" />
          </div>
          <Skeleton className="h-4 w-1/2 mb-5 mt-2" />
        </article>
      );
    }

    return (
      <article
        id="bank-info"
        className="mt-1.5 p-4 sm:col-span-5 bg-muted rounded-lg flex flex-col h-full overflow-auto scrollbar"
      >
        <div className="flex justify-between items-start">
          <LabelSection>{"HESAP ADI"}</LabelSection>
          <CopyButton
            text={selectedBank?.accountName || ""}
            aria-label="Hesap adını kopyala"
          />
        </div>
        <p className="text-foreground text-sm px-1 mb-5">
          {selectedBank?.accountName || "Hesap adı bulunamadı"}
        </p>
        <Separator />
        <div className="flex justify-between items-start">
          <LabelSection>{"IBAN NO"}</LabelSection>
          <CopyButton
            text={selectedBank?.iban || ""}
            aria-label="IBAN numarasını kopyala"
          />
        </div>
        <p className="text-foreground text-sm px-1 mb-5">
          {selectedBank?.iban || "IBAN bulunamadı"}
        </p>
        <Separator />
        <div className="flex justify-between items-start">
          <LabelSection>{"AÇIKLAMA"}</LabelSection>
          <CopyButton
            text={data?.descriptionCode || ""}
            aria-label="Açıklama kodunu kopyala"
          />
        </div>
        <p className="text-foreground text-sm px-1 mb-5">
          {data?.descriptionCode || "Açıklama kodu bulunamadı"}
        </p>
      </article>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form>
        <DialogContent className="sm:max-w-[640px]">
          <DialogHeader>
            <DialogTitle>{"Havale ile Para Yatır"}</DialogTitle>
          </DialogHeader>
          <section
            id="transfer-info"
            className="grid grid-cols-1 sm:grid-cols-8 gap-5 mx-2 sm:mx-4 sm:max-h-86"
          >
            <nav
              id="bank-list"
              className="sm:col-span-3 overflow-auto scrollbar"
            >
              {renderBankList()}
            </nav>
            {renderBankInfo()}
          </section>
          <Separator />
          <DialogFooter>
            <div className="flex gap-4 items-center mx-4 -mt-5 mb-4">
              <AlertShape>!</AlertShape>
              <p className="text-muted-foreground text-xs leading-5">
                {"Lütfen aktarım yapmadan önce "}
                <InfoDialog
                  trigger={
                    <button className="underline underline-offset-4 decoration-2 hover:text-foreground text-accent-foreground">
                      {"bilgilendirme metnini"}
                    </button>
                  }
                  title="Bilgilendirme"
                  items={[
                    "Sadece kendi adınıza ait hesaptan havale yapabilirsiniz.",
                    "Havale işlemi gerçekleştirirken açıklama kısmına mutlaka verilen kodu yazınız.",
                    "Para yatırma işlemi maksimum 3 iş günü içerisinde hesabınıza yansıyacaktır.",
                    "Yatırılan tutarın her 10 TL'lik kısmı 1 Soda'ya tekabül ederken, bu dönüşümden arta kalan bakiye Kapak birimine çevrilir.",
                    "Geciken havale işlemleri için destek ekibimizle iletişime geçin.",
                    "Para iadesi talep edilmesi durumunda EFT ücreti kesilerek iade yapılır. EFT ücretinden düşük tutarlı ödemeler iade edilmez.",
                  ]}
                />
                {" okuyun."}
              </p>
            </div>
          </DialogFooter>
        </DialogContent>
      </form>
      {children}
    </Dialog>
  );
}
