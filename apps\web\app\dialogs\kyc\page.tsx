"use client";

import { KYCDialog, type KYCStatus } from "@/components/dialogs/kyc.dialog";
import { useEffect, useState } from "react";

export default function Page() {
  const [status, setStatus] = useState<KYCStatus>("not_submitted");
  console.log(status);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle number keys 1-4 and prevent default behavior
      if (event.key >= "1" && event.key <= "4") {
        event.preventDefault();

        switch (event.key) {
          case "1":
            setStatus("not_submitted");
            break;
          case "2":
            setStatus("accepted");
            break;
          case "3":
            setStatus("rejected");
            break;
          case "4":
            setStatus("pending");
            break;
        }
      }
    };

    // Add event listener to document
    document.addEventListener("keydown", handleKeyDown);

    // Cleanup function to remove event listener
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  return <KYCDialog status={status} onStatusChange={setStatus} />;
}
