@import "tailwindcss";

@plugin 'tailwind-scrollbar';

@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.97 0 0);
  --foreground: oklab(29.276999999999997% -0.02007 -0.10749);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklab(82.902% 0.0281 0.13654);
  --primary-foreground: oklch(0% 0 0);
  --muted: oklab(89.755% 0 -0.0001);
  --muted-foreground: oklch(50.323% 0.00006 271.152);
  --accent: oklch(89.833% 0.00107 11.897);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(60% 0.2 33);
  --destructive-foreground: oklch(25% 0.2 33);
  --warning: oklch(60% 0.2 80);
  --warning-foreground: oklch(25% 0.2 66);
  --success: oklch(60% 0.2 160);
  --success-foreground: oklch(25% 0.2 140);
  --border: oklch(0% 0 0);
  --input: oklch(75.083% 0.00009 271.152);
  --ring: oklch(100% 0 0);
  --outline: oklab(82.902% 0.0281 0.13654);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.8rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --secondary: oklch(0.65 0.05 250);
  --secondary-foreground: oklch(0.98 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --warning: oklch(84.3% 0.19 102.21);
  --warning-foreground: oklch(31.8% 0.12 95.37);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.269 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
  --secondary: oklch(0.65 0.05 250);
  --secondary-foreground: oklch(0.98 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-outline: var(--outline);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
}

@layer base {
  * {
    @apply border-border outline-outline focus-visible:outline-solid focus-visible:outline-3;
  }

  :root {
    scrollbar-gutter: stable overlay;
  }

  html {
    @apply scrollbar-thumb-foreground scrollbar-track-[transparent];
    scroll-behavior: smooth;
  }
  body {
    @apply bg-background text-foreground;
    font-size: 1.2rem;
    letter-spacing: 1px;
    text-shadow:
      -1px -1px 0 var(--ring),
      1px -1px 0 var(--ring),
      -1px 1px 0 var(--ring),
      1px 1px 0 var(--ring);
  }

  button {
    @apply not-disabled:cursor-pointer;
  }

  img {
    @apply select-none pointer-events-none;
  }

  /* Chrome, Safari, Edge, Opera */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }
}

@utility fake-text-stroke {
  text-shadow:
    -1px -1px 0 var(--ring),
    1px -1px 0 var(--ring),
    -1px 1px 0 var(--ring),
    1px 1px 0 var(--ring);
}

@utility fake-text-stroke-muted {
  text-shadow:
    -1px -1px 0 color-mix(in srgb, var(--ring) 30%, transparent),
    1px -1px 0 color-mix(in srgb, var(--ring) 30%, transparent),
    -1px 1px 0 color-mix(in srgb, var(--ring) 30%, transparent),
    1px 1px 0 color-mix(in srgb, var(--ring) 30%, transparent);
}

@utility fake-text-stroke-black {
  text-shadow:
    -1px -1px 0 var(--border),
    1px -1px 0 var(--border),
    -1px 1px 0 var(--border),
    1px 1px 0 var(--border);
}

@utility grid-background {
  background-color: color-mix(in srgb, var(--foreground) 7%, transparent);
  background-image:
    linear-gradient(var(--background) 2px, transparent 2px),
    linear-gradient(90deg, var(--background) 2px, transparent 2px);
  background-size: 30px 30px;
  background-position:
    0 0,
    0 0;
}

@utility grid-background-avatar {
  background-image:
    linear-gradient(
      color-mix(in srgb, var(--input) 50%, transparent) 2px,
      transparent 2px
    ),
    linear-gradient(
      90deg,
      color-mix(in srgb, var(--input) 50%, transparent) 2px,
      transparent 2px
    );
  background-size: 20px 20px;
  background-position:
    0 0,
    0 0;
}
