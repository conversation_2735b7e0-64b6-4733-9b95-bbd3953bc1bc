import * as React from "react";
import { Star } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";

export interface StarRatingProps {
  /** Current rating value (0-5) */
  value?: number;
  /** Maximum number of stars (default: 5) */
  maxStars?: number;
  /** Size of the stars */
  size?: "small" | "medium" | "large";
  /** Custom className */
  className?: string;
}

const sizeClasses = {
  small: "size-4",
  medium: "size-6",
  large: "size-8",
};

const spaceClasses = {
  small: "-space-x-0.5",
  medium: "-space-x-0.5",
  large: "-space-x-1.5",
};

export function StarRating({
  value = 0,
  maxStars = 5,
  size = "medium",
  className,
}: StarRatingProps) {
  return (
    <div
      className={cn("flex items-center", spaceClasses[size], className)}
      role="img"
      aria-label={`Rating: ${value} stars`}
    >
      {Array.from({ length: maxStars }, (_, index) => {
        const isFilled = index < value;
        return (
          <div
            key={index}
            className="relative"
            aria-label={`${index + 1} star${index === 0 ? "" : "s"}`}
          >
            <Star
              className={cn(
                "stroke-border stroke-1",
                sizeClasses[size],
                isFilled
                  ? "fill-primary"
                  : "fill-background stroke-muted-foreground",
              )}
            />
          </div>
        );
      })}
    </div>
  );
}
