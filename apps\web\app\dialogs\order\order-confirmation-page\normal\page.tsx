import { OrderConfirmationDialog } from "@/components/dialogs/order-confirmation.dialog";
import avatar from "./avatar.png";
import activity from "./activity.jpg";

export default async function Page() {
  return (
    <OrderConfirmationDialog
      open={true}
      username="<PERSON><PERSON>-chan"
      unitCount={3}
      unitType="saatlik"
      activityTitle="Valorant"
      avatar={{
        src: avatar.src,
        alt: "avatar",
      }}
      activity={{
        src: activity.src,
        alt: "activity",
      }}
      mainItem={{
        name: "Boost",
        sodaAmount: "52",
      }}
      serviceModifiers={[{ name: "Maid Cosplay", sodaAmount: "10" }]}
      totalSodaAmount={240}
    />
  );
}
