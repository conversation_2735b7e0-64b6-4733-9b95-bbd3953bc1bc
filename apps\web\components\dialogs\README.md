# Dialog Components Guide (Implementation + Style Guide)

This document describes the dialog system used in apps/web/components/dialogs. It consolidates the shared architecture, styling, state and accessibility patterns across existing dialogs and sets guidance for building new ones consistently.

## Overview

- Dialogs are built on `@workspace/ui` components (Radix-based) and styled with Tailwind.
- Composition is consistent: Dialog > DialogContent > (DialogHeader, main content, DialogFooter).
- Dialogs are controlled (open/onOpenChange) and often host nested/dialog flows (e.g., KYC → Bank Transfer).
- Forms use TanStack Form with Zod validation; success is communicated via a secondary FeedbackDialog.
- Many flows intentionally prevent accidental close (ESC/outside click) until an explicit action is taken.

## Architecture

Common architectural patterns:

- Controlled dialogs: All dialogs accept open?: boolean and onOpenChange?: (open: boolean) => void and render <Dialog open onOpenChange> around content.
- Trigger slot pattern: For dialogs that can be triggered inline (e.g., InfoDialog), expose a trigger?: React.ReactNode and render <DialogTrigger asChild>.
- Nested dialogs: Parent dialogs keep their overlay; child dialogs are rendered as siblings of the parent <Dialog> (not inside content) to preserve proper overlay stacking and history. Example: WalletDialog renders KYCDialog, BankTransferDialog, IbanDialog, WithdrawalDialog as siblings.
- Overlay management: Transactional dialogs often disable outside and escape close via onEscapeKeyDown/onInteractOutside with e.preventDefault(). Side-sheets can be anchored inside a dialog via a container portal (see ActivityBrowserDialog → CategorySheet with overlay={false} and container ref inside DialogContent).
- History integration: Support deep-linking/back by passing historyKey to Dialog and using useDialogHistory per child dialog.
- Composition: Header holds title and controls; content holds scrollable UI; footer collects primary/secondary actions.

## Component Structure and Naming

- File naming: <feature>.dialog.tsx (PascalCase exported symbol + Dialog suffix), colocated under dialogs/.
- Always "use client" at the top; export a single React component per dialog and its Typescript props interface.
- Keep UI-only helper subcomponents in the same file when tightly coupled (e.g., LoadingSkeleton, ActivityItem).
- Prefer semantic sectioning and stable testable ids where helpful (ids like summary, bank-info).

Example skeleton:

```tsx
"use client";
export interface SomeDialogProps {
  open?: boolean;
  onOpenChange?: (o: boolean) => void;
}
export function SomeDialog({ open = false, onOpenChange }: SomeDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>...</DialogContent>
    </Dialog>
  );
}
```

## Props Interface Patterns

- Core props on most dialogs:
  - open?: boolean
  - onOpenChange?: (open: boolean) => void
  - showCloseButton?: boolean (default chosen by flow; often false for forced flows)
  - children?: React.ReactNode (when the dialog needs to render triggers or external content)
- Flow props:
  - historyKey?: string for back/forward support (WalletDialog and children)
  - trigger?: React.ReactNode on InfoDialog to support slot-trigger pattern
- Domain props: Each dialog defines its own data (e.g., OrderConfirmationDialog.mainItem, serviceModifiers).

TypeScript example:

```ts
export interface MyDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  showCloseButton?: boolean;
  children?: React.ReactNode;
}
```

## Styling Guidelines

- Tailwind + design tokens:
  - Use bg-muted, text-muted-foreground for secondary blocks; bg-background/foreground for core surfaces.
  - Buttons: size="small" by default for compact icon+text; variant="primary" for primary actions.
  - Titles may use variant="stripe" for branded headings.
  - Use consistent borders/rings: border-2/3 and ring-2 with foreground/background to achieve card-like look.
- Layout:
  - Set dialog width via sm:max-w-[...]; large, data-rich dialogs can use flex and explicit heights with overflow-hidden and internal scroll areas (h-[80vh], max-h, etc.).
  - Make long lists scrollable within content areas; add className="scrollbar" to enable custom scroll styling.
  - Tables should stretch toward the bottom of the dialog: set DialogContent to flex-col and let the table’s container flex-1.
- Icons & images:
  - Prefer lucide-react icons; keep sizes small and consistent (e.g., size-4/5/6). Wrap icon-only buttons with appropriate aria-labels.
  - Images often have border-2/3 and ring-2 to fit visual language; use rounded-sm.
- Feedback affordances:
  - Use Skeleton for loading placeholders in lists/tables/cards.
  - Use Badge variants for status chips (success/warning/destructive/default).

## State Management

- Open/close: Dialogs are controlled by parent state; child dialogs’ open is derived from user state and interaction (see Wallet → KYC/IBAN gating).
- Preventing accidental close: For transactional flows, set onEscapeKeyDown/onInteractOutside to prevent default and rely on explicit close buttons.
- Forms:
  - Use TanStack Form with revalidateLogic (mode: "blur", modeAfterSubmission: "change" or as needed).
  - Validate with Zod schemas; for dynamic constraints, compute schema with useMemo.
  - Expose processing states to buttons via processing prop and disable inputs appropriately.
  - On success, open a FeedbackDialog and optionally close the parent after dismiss.
- Data fetching:
  - Use React Query hooks; separate count/pagination when necessary; show isLoading skeletons and keep isFetching lightweight.
  - Keep pagination local to dialog content (see ActivityBrowserDialog ActivityPagination component).

## Accessibility

- Always render DialogTitle and (when needed) DialogDescription.
- Respect focus management (Radix handles trapping). When blocking ESC/outside, ensure an explicit close or completion path is present.
- Provide aria-labels for icon-only buttons (e.g., favorites heart, receipt, support buttons).
- For destructive actions, use AlertDialog to present a confirm step instead of custom modals.

## Usage Examples

Minimal controlled dialog with trigger slot:

```tsx
<InfoDialog trigger={<button>Bilgilendirme</button>} items={["Madde 1"]} />
```

Prevent outside/Escape closing for transactional flows:

```tsx
<DialogContent
  onEscapeKeyDown={(e) => e.preventDefault()}
  onInteractOutside={(e) => e.preventDefault()}
>
  ...
</DialogContent>
```

Nested success feedback pattern:

```tsx
<FeedbackDialog open={show} onOpenChange={setShow} message="Başarılı" />
```

Conditional child dialogs (KYC → Bank Transfer):

```tsx
user.is_kyc_approved ? setBankTransferOpen(true) : setKycOpen(true);
```

Anchor side-sheet inside a dialog content (portal container):

```tsx
<SheetContent overlay={false} container={containerRef.current!} side="right" />
```

Deletion confirmation via AlertDialog:

```tsx
<AlertDialog>
  <AlertDialogTrigger asChild>
    <button>Sil</button>
  </AlertDialogTrigger>
</AlertDialog>
```

## Best Practices (Do’s & Don’ts)

Do:

- Keep dialogs controlled and predictable (open/onOpenChange at the top level).
- Use trigger slots for inline triggers (InfoDialog) and children for composition when necessary.
- Block accidental closes for forms and multi-step flows; provide explicit buttons to proceed/close.
- Use TanStack Form + Zod with dynamic validation where needed and surface errors next to fields.
- Place nested dialogs as siblings of the parent <Dialog> for correct overlay stacking.
- Use useDialogHistory and unique historyKey for deep-linkable flows.
- Compose headers/footers consistently; keep actions in DialogFooter.
- Use Skeletons for loading; keep lists/tables scrollable; ensure tables stretch to the bottom.
- Use AlertDialog for destructive confirmations (maintain icon-button visuals via asChild triggers).
- Localize UI text (Turkish) and respect existing phrasing.

Don’t:

- Don’t manage persistent internal open state that conflicts with parent control.
- Don’t allow ESC/outside-close to abort critical flows; avoid accidental data loss.
- Don’t break visual language (e.g., oversized icons, inconsistent borders/rings, or arbitrary spacings).
- Don’t couple domain fetching logic directly to UI without proper hooks; keep dialogs presentational where possible.
- Don’t highlight transactional rows unconditionally; e.g., in Wallet, ensure “Talep” rows aren’t primary-highlighted when the filter isolates them.

## Reference: Existing Dialogs and Notable Patterns

- ActivityBrowserDialog: Complex content with search, favorites toggle, category side-sheet anchored via container; custom pagination and loading skeletons.
- BankTransferDialog: Master-detail with selectable bank cards and info panel; InfoDialog usage in footer notice.
- FeedbackDialog: Reusable success/feedback modal; optional showCloseButton; message slot supports custom icons.
- IbanDialog: Form with masked/segmented input, paste handling, and success feedback; nested FeedbackDialog closes parent as needed.
- InfoDialog: Trigger-slot pattern; centered content list; no close button by default.
- KYCDialog: ID-card style layout; ApprovalButton for disclaimer; multi-attempt submit simulation; status badge component.
- OrderConfirmationDialog: Summary layout with images, totals, and support button; DialogClose wraps primary submit.
- WalletDialog: Large modal with tabs, filters, search, CSV export; child dialogs for KYC/IBAN/Transfer/Withdrawal; prevention of outside-close.
- WithdrawalDialog: Numeric input with button group; dynamic validation/min-max logic; derived TL computations and feedback dialog.
