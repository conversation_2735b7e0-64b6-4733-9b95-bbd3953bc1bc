import { OrderConfirmationDialog } from "@/components/dialogs/order-confirmation.dialog";
import avatar from "./avatar.gif";
import activity from "./activity.jpg";

export default async function Page() {
  return (
    <OrderConfirmationDialog
      open={true}
      username="Heaven ♥ ✨-chan"
      unitCount={2}
      unitType="30 dakikalık"
      activityTitle="Stardew Valley"
      avatar={{
        src: avatar.src,
        alt: "avatar",
      }}
      activity={{
        src: activity.src,
        alt: "activity",
      }}
      mainItem={{
        name: "Rate Farm",
        sodaAmount: "20",
      }}
      totalSodaAmount={40}
    />
  );
}
