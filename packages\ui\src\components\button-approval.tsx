"use client";

import * as React from "react";
import { Button } from "@workspace/ui/components/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { cn } from "@workspace/ui/lib/utils";

export type ApprovalButtonProps = Omit<
  React.ComponentProps<typeof Button>,
  "pending" | "processing"
> & {
  children?: React.ReactNode;
  onOpen?: () => void;
  needApproval?: boolean;
  asChild?: boolean;
  disclaimer?: React.ReactNode;
  approveText?: string;
  cancelText?: string;
  onApprove?: () => void | Promise<void>;
  onCancel?: () => void | Promise<void>;
  open?: boolean;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  align?: React.ComponentProps<typeof PopoverContent>["align"];
  side?: React.ComponentProps<typeof PopoverContent>["side"];
  sideOffset?: number;
  contentClassName?: string;
};

export function ApprovalButton({
  children,
  // Popover props
  disclaimer = "Devam etmek istediğinize emin misiniz?",
  approveText = "Onayla",
  cancelText = "İptal",
  onApprove,
  onCancel,
  open,
  defaultOpen,
  onOpenChange,
  align = "center",
  side = "top",
  sideOffset,
  contentClassName,
  // Button props
  disabled,
  onOpen,
  needApproval = true,
  asChild = false,
  ...buttonProps
}: ApprovalButtonProps) {
  const isControlled = open !== undefined;
  const [internalOpen, setInternalOpen] = React.useState<boolean>(
    defaultOpen ?? false,
  );
  const actualOpen = isControlled ? (open as boolean) : internalOpen;

  // Internal visual states for the trigger button
  const [pending, setPending] = React.useState(false);
  const [processing, setProcessing] = React.useState(false);

  // When popover opens, show pending visuals; when it closes, clear pending
  const handleOpenChange = React.useCallback(
    (next: boolean) => {
      if (!isControlled) setInternalOpen(next);
      setPending(next);
      // Do not touch `processing` here so that, after approval, the trigger
      // can remain in processing state while the async onApprove completes.

      // Call onOpen callback when dialog opens
      if (next) {
        onOpen?.();
      }

      onOpenChange?.(next);
    },
    [isControlled, onOpenChange, onOpen],
  );

  // Wrap onApprove to manage processing visuals while promise resolves
  const handleApprove = React.useCallback(() => {
    setPending(false);
    setProcessing(true);

    try {
      const result = onApprove?.();
      if (result && typeof (result as any).then === "function") {
        (result as Promise<void>).finally(() => {
          setProcessing(false);
          handleOpenChange(false);
        });
      } else {
        setProcessing(false);
        handleOpenChange(false);
      }
    } catch (error) {
      // Swallow error to ensure popover closes
      setProcessing(false);
      handleOpenChange(false);
    }
  }, [onApprove, handleOpenChange]);

  const handleCancel = React.useCallback(() => {
    // Clear pending if user cancels; processing should already be false here
    setPending(false);
    onCancel?.();
    handleOpenChange(false);
  }, [onCancel, handleOpenChange]);

  // Handle direct approval when needApproval is false
  const handleButtonClick = React.useCallback(() => {
    if (!needApproval) {
      setProcessing(true);
      const result = onApprove?.();
      if (result && typeof (result as any).then === "function") {
        (result as Promise<void>).finally(() => setProcessing(false));
      } else {
        setProcessing(false);
      }
    }
  }, [needApproval, onApprove]);

  if (!needApproval) {
    return (
      <Button
        disabled={disabled || processing}
        processing={processing}
        onClick={handleButtonClick}
        asChild={asChild}
        {...buttonProps}
      >
        {children}
      </Button>
    );
  }

  return (
    <Popover open={actualOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        {asChild ? (
          children
        ) : (
          <Button
            disabled={disabled || processing || pending}
            pending={pending}
            processing={processing}
            {...buttonProps}
          >
            {children}
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent
        align={align}
        side={side}
        sideOffset={sideOffset}
        onInteractOutside={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        className={cn(
          "pointer-events-auto space-y-6 w-[460px] py-5 before:border-t-4 before:border-b-2 before:border-foreground before:block before:h-2.5 before:mb-4",
          contentClassName,
        )}
      >
        <p className="mx-auto text-center text-sm leading-6 text-pretty text-foreground">
          {disclaimer}
        </p>
        <div className="mx-auto max-w-[250px] grid grid-cols-2 gap-2">
          <Button
            size="small"
            variant="default"
            onClick={handleCancel}
            className="w-full"
          >
            {cancelText}
          </Button>
          <Button
            size="small"
            variant="secondary"
            onClick={handleApprove}
            className="w-full"
          >
            {approveText}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

export default ApprovalButton;
